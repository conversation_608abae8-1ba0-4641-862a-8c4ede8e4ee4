/**
 * ProductService V4 版本测试脚本
 * 测试基于参数字段提取的智能产品对比功能 - JSON格式输出
 * 功能: 从productService_v4获取AI分析结果并保存原始JSON数据到文件
 * 测试产品: 华为 Mate 70 Pro vs 苹果iPhone 16 Pro
 * 重点测试: 动态参数提取和完整性验证
 */

const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
const { compareProductsByNamesV4 } = require('../src/services/product/productService_v4');

// 数据库连接配置
const DB_URL = process.env.MONGODB_URI || 'mongodb://localhost:27017/xuanxuan_test';

/**
 * 连接数据库
 */
async function connectDB() {
  try {
    await mongoose.connect(DB_URL);
    console.log('✅ 数据库连接成功');
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    process.exit(1);
  }
}

/**
 * 断开数据库连接
 */
async function disconnectDB() {
  try {
    await mongoose.disconnect();
    console.log('✅ 数据库连接已断开');
  } catch (error) {
    console.error('❌ 断开数据库连接失败:', error.message);
  }
}

/**
 * 验证V4版本JSON结构的完整性（包含新的参数分析功能）
 * @param {Object} structuredReport 结构化报告对象
 * @param {Object} parameterAnalysis 参数分析对象
 * @returns {Object} 验证结果
 */
function validateV4JSONStructure(structuredReport, parameterAnalysis) {
  const validation = {
    isValid: true,
    errors: [],
    warnings: []
  };

  // 检查必需的顶级字段
  const requiredFields = ['summary', 'technicalSpecs', 'prosAndCons', 'usageScenarios', 'purchaseAdvice'];
  requiredFields.forEach(field => {
    if (!structuredReport[field]) {
      validation.isValid = false;
      validation.errors.push(`缺少必需字段: ${field}`);
    }
  });

  // V4新增：检查summary中的参数分析字段
  if (structuredReport.summary) {
    const v4SummaryFields = ['analyzedParameters', 'parameterCategories'];
    v4SummaryFields.forEach(field => {
      if (!structuredReport.summary[field]) {
        validation.warnings.push(`V4 summary缺少新字段: ${field}`);
      }
    });
  }

  // V4新增：检查technicalSpecs中的parameters字段
  if (structuredReport.technicalSpecs && Array.isArray(structuredReport.technicalSpecs)) {
    structuredReport.technicalSpecs.forEach((category, index) => {
      if (!category.parameters) {
        validation.warnings.push(`technicalSpecs[${index}]缺少V4新增的parameters字段`);
      }
      
      // 检查items中的values结构
      if (category.items && Array.isArray(category.items)) {
        category.items.forEach((item, itemIndex) => {
          if (!item.values) {
            validation.warnings.push(`technicalSpecs[${index}].items[${itemIndex}]缺少V4新增的values字段`);
          }
        });
      }
    });
  }

  // V4新增：检查usageScenarios中的relevantParameters字段
  if (structuredReport.usageScenarios && Array.isArray(structuredReport.usageScenarios)) {
    structuredReport.usageScenarios.forEach((scenario, index) => {
      if (!scenario.relevantParameters) {
        validation.warnings.push(`usageScenarios[${index}]缺少V4新增的relevantParameters字段`);
      }
    });
  }

  // V4新增：检查purchaseAdvice中的keyParameters字段
  if (structuredReport.purchaseAdvice && structuredReport.purchaseAdvice.specificAdvice) {
    if (Array.isArray(structuredReport.purchaseAdvice.specificAdvice)) {
      structuredReport.purchaseAdvice.specificAdvice.forEach((advice, index) => {
        if (!advice.keyParameters) {
          validation.warnings.push(`purchaseAdvice.specificAdvice[${index}]缺少V4新增的keyParameters字段`);
        }
      });
    }
  }

  // V4新增：验证参数分析的完整性
  if (parameterAnalysis) {
    const requiredParamFields = ['totalParameters', 'parametersByCategory', 'extractedCategories'];
    requiredParamFields.forEach(field => {
      if (!parameterAnalysis[field]) {
        validation.errors.push(`参数分析缺少必需字段: ${field}`);
        validation.isValid = false;
      }
    });

    // 检查参数分类是否在技术规格中都有对应
    if (parameterAnalysis.extractedCategories && structuredReport.technicalSpecs) {
      const reportCategories = new Set(structuredReport.technicalSpecs.map(spec => spec.category));
      parameterAnalysis.extractedCategories.forEach(category => {
        if (!reportCategories.has(category)) {
          validation.warnings.push(`参数分类 "${category}" 在技术规格分析中缺失`);
        }
      });
    }
  }

  return validation;
}

/**
 * 验证参数提取的完整性
 * @param {Object} parameterAnalysis 参数分析结果
 * @returns {Object} 验证结果
 */
function validateParameterExtraction(parameterAnalysis) {
  const validation = {
    isValid: true,
    issues: [],
    stats: {}
  };

  if (!parameterAnalysis) {
    validation.isValid = false;
    validation.issues.push('参数分析对象为空');
    return validation;
  }

  // 统计信息
  validation.stats = {
    totalParameters: parameterAnalysis.totalParameters || 0,
    totalCategories: parameterAnalysis.extractedCategories ? parameterAnalysis.extractedCategories.length : 0,
    productTypes: parameterAnalysis.productTypes || [],
    isMixedTypes: parameterAnalysis.isMixedTypes || false
  };

  // 检查参数覆盖率
  if (parameterAnalysis.parameterCoverage) {
    const coverageStats = Object.values(parameterAnalysis.parameterCoverage);
    const fullCoverageParams = coverageStats.filter(param => param.coverage === '100%').length;
    const partialCoverageParams = coverageStats.length - fullCoverageParams;
    
    validation.stats.fullCoverageParameters = fullCoverageParams;
    validation.stats.partialCoverageParameters = partialCoverageParams;
    validation.stats.averageCoverage = coverageStats.length > 0 ? 
      (coverageStats.reduce((sum, param) => sum + parseFloat(param.coverage), 0) / coverageStats.length).toFixed(1) + '%' : '0%';
  }

  // 检查分类参数分布
  if (parameterAnalysis.parametersByCategory) {
    const categoryStats = Object.keys(parameterAnalysis.parametersByCategory).map(category => ({
      category: category,
      parameterCount: parameterAnalysis.parametersByCategory[category].length
    })).sort((a, b) => b.parameterCount - a.parameterCount);
    
    validation.stats.categoryDistribution = categoryStats;
  }

  return validation;
}

/**
 * 测试产品对比功能 - V4版本主要测试
 */
async function testProductComparisonV4() {
  console.log('\n🆚 测试: 产品对比功能 V4 (华为 Mate 70 Pro vs 苹果iPhone 16 Pro)');
  console.log('='.repeat(80));

  const productNames = [
    '华为 Mate 70 Pro',       // 使用数据库中实际存在的产品名称
    '苹果iPhone 16 Pro'       // 使用数据库中实际存在的产品名称
  ];

  console.log(`📱 对比产品: ${productNames.join(' vs ')}`);
  console.log(`💡 注意: V4版本基于动态参数提取，确保分析完整性`);

  try {
    const startTime = Date.now();

    const result = await compareProductsByNamesV4(productNames);

    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log(`⏱️ 对比耗时: ${duration}ms`);

    if (result.success) {
      console.log('\n✅ 产品对比成功!');

      // 显示基本信息
      console.log('\n📊 对比基本信息:');
      console.log(`   请求的产品: ${result.data.requestedProducts.join(', ')}`);
      console.log(`   找到的产品: ${result.data.foundProducts.join(', ')}`);
      console.log(`   未找到产品: ${result.data.notFoundProducts.length > 0 ? result.data.notFoundProducts.join(', ') : '无'}`);
      console.log(`   产品数量: ${result.data.productCount}`);
      console.log(`   对比版本: ${result.data.version}`);
      console.log(`   分析方法: ${result.data.analysisMethod}`);

      // V4新增：显示参数分析信息
      if (result.data.parameterAnalysis) {
        console.log('\n🔍 V4参数分析信息:');
        const paramAnalysis = result.data.parameterAnalysis;
        console.log(`   产品类型: ${paramAnalysis.productTypes.join(', ')}`);
        console.log(`   主要类型: ${paramAnalysis.primaryProductType}`);
        console.log(`   混合类型: ${paramAnalysis.isMixedTypes ? '是' : '否'}`);
        console.log(`   总参数数量: ${paramAnalysis.totalParameters}`);
        console.log(`   参数分类数量: ${paramAnalysis.extractedCategories.length}`);

        // 显示参数分类分布
        if (paramAnalysis.parametersByCategory) {
          console.log('\n   📋 参数分类分布:');
          Object.keys(paramAnalysis.parametersByCategory).forEach((category, index) => {
            const paramCount = paramAnalysis.parametersByCategory[category].length;
            console.log(`      ${index + 1}. ${category}: ${paramCount}个参数`);
          });
        }

        // 验证参数提取完整性
        const paramValidation = validateParameterExtraction(paramAnalysis);
        console.log('\n   🔬 参数提取验证:');
        console.log(`      验证状态: ${paramValidation.isValid ? '✅ 通过' : '❌ 失败'}`);
        console.log(`      总参数: ${paramValidation.stats.totalParameters}`);
        console.log(`      总分类: ${paramValidation.stats.totalCategories}`);
        if (paramValidation.stats.fullCoverageParameters !== undefined) {
          console.log(`      完全覆盖参数: ${paramValidation.stats.fullCoverageParameters}`);
          console.log(`      部分覆盖参数: ${paramValidation.stats.partialCoverageParameters}`);
          console.log(`      平均覆盖率: ${paramValidation.stats.averageCoverage}`);
        }

        if (paramValidation.issues.length > 0) {
          console.log('      问题:');
          paramValidation.issues.forEach(issue => {
            console.log(`        - ${issue}`);
          });
        }
      }

      // 显示产品详细信息
      console.log('\n📱 产品详细信息:');
      result.data.products.forEach((product, index) => {
        console.log(`\n   ${index + 1}. ${product.displayName}`);
        console.log(`      品牌: ${product.brand}`);
        console.log(`      类型: ${product.productType}`);
        console.log(`      价格: ${product.price ? `¥${product.price}` : product.priceRange || '暂无价格'}`);
        console.log(`      配置数量: ${product.configurations.length}`);
        console.log(`      默认配置: ${product.defaultConfiguration}`);
        console.log(`      支持对比: ${product.supportsComparison ? '是' : '否'}`);
      });

      // 显示 AI 分析结果
      console.log('\n🤖 AI 分析结果:');
      const aiAnalysis = result.data.aiAnalysis;
      console.log(`   产品类别: ${aiAnalysis.productCategory || '未识别'}`);
      console.log(`   同类别产品: ${aiAnalysis.isSameCategory ? '是' : '否'}`);
      console.log(`   输出格式: ${aiAnalysis.outputFormat || '未知'}`);
      console.log(`   分析方法: ${aiAnalysis.analysisMethod || '未知'}`);

      if (aiAnalysis.crossCategoryNote) {
        console.log(`   跨类别说明: ${aiAnalysis.crossCategoryNote}`);
      }

      console.log(`   分析时间: ${aiAnalysis.analysisTimestamp || '未知'}`);

      // 重点测试：验证V4结构化报告
      if (aiAnalysis.structuredReport) {
        console.log('\n🔍 V4 JSON结构验证:');
        const validation = validateV4JSONStructure(aiAnalysis.structuredReport, result.data.parameterAnalysis);

        if (validation.isValid) {
          console.log('   ✅ JSON结构验证通过');
        } else {
          console.log('   ❌ JSON结构验证失败');
          validation.errors.forEach(error => {
            console.log(`      错误: ${error}`);
          });
        }

        if (validation.warnings.length > 0) {
          console.log('   ⚠️ 警告:');
          validation.warnings.forEach(warning => {
            console.log(`      ${warning}`);
          });
        }

        // 显示V4结构化报告的各个部分
        const report = aiAnalysis.structuredReport;

        console.log('\n📋 V4结构化报告内容:');

        // 摘要部分（V4增强）
        if (report.summary) {
          console.log(`\n   📝 摘要:`);
          console.log(`      标题: ${report.summary.title || '未知'}`);
          console.log(`      产品数量: ${report.summary.productCount || 0}`);
          console.log(`      类别: ${report.summary.category || '未知'}`);
          console.log(`      分析参数数量: ${report.summary.analyzedParameters || '未知'}`);
          if (report.summary.parameterCategories && Array.isArray(report.summary.parameterCategories)) {
            console.log(`      参数分类: ${report.summary.parameterCategories.join(', ')}`);
          }
          if (report.summary.keyDifferences && Array.isArray(report.summary.keyDifferences)) {
            console.log(`      关键差异 (${report.summary.keyDifferences.length}项):`);
            report.summary.keyDifferences.forEach((diff, index) => {
              console.log(`        ${index + 1}. ${diff}`);
            });
          }
        }

        // 技术规格部分（V4增强）
        if (report.technicalSpecs && Array.isArray(report.technicalSpecs)) {
          console.log(`\n   🔧 技术规格对比 (${report.technicalSpecs.length}个类别):`);
          report.technicalSpecs.forEach((category, categoryIndex) => {
            console.log(`      ${categoryIndex + 1}. ${category.category || '未知类别'}`);
            if (category.parameters && Array.isArray(category.parameters)) {
              console.log(`         预定义参数: ${category.parameters.join(', ')}`);
            }
            if (category.items && Array.isArray(category.items)) {
              console.log(`         分析项目: ${category.items.length}个`);
              category.items.slice(0, 2).forEach((item) => {
                console.log(`         - ${item.name || '未知参数'}`);
                if (item.values && Array.isArray(item.values)) {
                  console.log(`           产品值: ${item.values.length}个`);
                }
              });
              if (category.items.length > 2) {
                console.log(`         ... 还有${category.items.length - 2}个参数`);
              }
            }
          });
        }

        // 使用场景推荐部分（V4增强）
        if (report.usageScenarios && Array.isArray(report.usageScenarios)) {
          console.log(`\n   🎯 使用场景推荐 (${report.usageScenarios.length}个场景):`);
          report.usageScenarios.forEach((scenario, index) => {
            console.log(`      ${index + 1}. ${scenario.scenario || '未知场景'}`);
            console.log(`         推荐产品: ${scenario.recommendedProduct || '未知'}`);
            if (scenario.relevantParameters && Array.isArray(scenario.relevantParameters)) {
              console.log(`         相关参数: ${scenario.relevantParameters.join(', ')}`);
            }
          });
        }

        // 保存V4版本的JSON数据到文件
        try {
          const v4FileName = 'v4.json';
          const v4FilePath = path.join(__dirname, v4FileName);

          // 保存从productService_v4获取的原始数据
          fs.writeFileSync(v4FilePath, JSON.stringify(result, null, 2), 'utf8');
          console.log(`\n📄 V4版本JSON数据已保存到文件: ${v4FilePath}`);
          console.log(`📂 文件大小: ${fs.statSync(v4FilePath).size} 字节`);

        } catch (saveError) {
          console.error(`❌ 保存V4报告文件失败: ${saveError.message}`);
        }

      } else {
        console.log('\n❌ 未找到结构化报告');

        // 检查是否有解析错误
        if (aiAnalysis.structuredReport && aiAnalysis.structuredReport.parseError) {
          console.log(`   解析错误: ${aiAnalysis.structuredReport.parseError}`);
          if (aiAnalysis.structuredReport.rawAnalysis) {
            console.log(`   原始分析长度: ${aiAnalysis.structuredReport.rawAnalysis.length} 字符`);
          }
        }
      }

    } else {
      console.log(`❌ 产品对比失败: ${result.error}`);
    }

  } catch (error) {
    console.log(`❌ 对比过程异常: ${error.message}`);
    console.error('详细错误:', error.stack);
  }
}

/**
 * 测试错误处理功能
 */
async function testErrorHandling() {
  console.log('\n🚨 测试: 错误处理功能');
  console.log('='.repeat(80));

  // 测试1: 产品数量不足
  console.log('\n测试1: 产品数量不足');
  try {
    const result = await compareProductsByNamesV4(['华为 Mate 70 Pro']);
    console.log(`   结果: ${result.success ? '成功' : '失败'}`);
    if (!result.success) {
      console.log(`   错误信息: ${result.error}`);
    }
  } catch (error) {
    console.log(`   异常: ${error.message}`);
  }

  // 测试2: 不存在的产品
  console.log('\n测试2: 不存在的产品');
  try {
    const result = await compareProductsByNamesV4(['不存在的产品A', '不存在的产品B']);
    console.log(`   结果: ${result.success ? '成功' : '失败'}`);
    if (!result.success) {
      console.log(`   错误信息: ${result.error}`);
    }
  } catch (error) {
    console.log(`   异常: ${error.message}`);
  }
}

/**
 * 主测试函数
 */
async function runAllTestsV4() {
  console.log('🚀 ProductService V4 测试开始');
  console.log('='.repeat(80));
  console.log(`测试时间: ${new Date().toLocaleString()}`);
  console.log(`Node.js版本: ${process.version}`);
  console.log(`测试重点: 动态参数提取和完整性验证`);

  try {
    // 连接数据库
    await connectDB();

    // 执行主要产品对比测试
    await testProductComparisonV4();

    // 执行错误处理测试
    await testErrorHandling();

    console.log('\n✅ 所有测试完成!');
    console.log('\n📊 测试总结:');
    console.log('   - 主要功能测试: V4动态参数提取和对比分析');
    console.log('   - 参数完整性验证: 确保所有参数都被分析');
    console.log('   - JSON结构验证: V4增强的数据结构');
    console.log('   - 文件输出测试: 保存为v4.json');

  } catch (error) {
    console.error('\n❌ 测试过程中发生错误:', error.message);
    console.error('详细错误:', error.stack);
  } finally {
    // 断开数据库连接
    await disconnectDB();
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  // 检查环境变量
  console.log('🔧 环境检查:');
  console.log(`   MongoDB URI: ${DB_URL}`);
  console.log(`   DEEPSEEK_API_KEY: ${process.env.DEEPSEEK_API_KEY ? '已设置' : '未设置'}`);

  if (!process.env.DEEPSEEK_API_KEY) {
    console.warn('⚠️ 警告: DEEPSEEK_API_KEY 环境变量未设置，AI 分析功能可能无法正常工作');
  }

  runAllTestsV4().catch(error => {
    console.error('测试运行失败:', error.message);
    process.exit(1);
  });
}

module.exports = {
  testProductComparisonV4,
  testErrorHandling,
  validateV4JSONStructure,
  validateParameterExtraction,
  runAllTestsV4
};
